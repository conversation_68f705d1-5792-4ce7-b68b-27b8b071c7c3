<script lang="ts" setup name="GreyClothTicketInspection">
import { computed, onMounted, onUnmounted, reactive, ref, watch } from 'vue'
import type { FormInstance } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'
import DefectInfoDialog from './components/DefectInfoDialog.vue'
import DefectRecordsDialog from './components/DefectRecordsDialog.vue'
import ElectronicScaleSettings from '@/components/ElectronicScaleSettings/index.vue'
import { getInfoBasicDefectlistEnum } from '@/api/fpQualityCheck'
import { useMeasureWeightStore } from '@/stores/measureWeight'
import { BatchUpdateGlobalConfigList, GetGlobalConfigDropdownList } from '@/api/globalConfig'
import { GlobalEnum } from '@/common/enum'
import { useMeasureWeightService } from '@/use/useMeasureWeightService'
import { readUpperNumberCommand, useMeasureMeterService } from '@/use/useMeasurMeterService'
import { useMeasureMeterStore } from '@/stores/measureMeter'

// 表单数据
const formData = reactive({
  grade: '', // 等级
  barcode: '', // 条码
  machineNo: '', // 机号
  weaver: '', // 织工
  inspector: '', // 查布
  weighing: 0, // 称重
  actualWeight: 0, // 实重
})

// 显示信息
const displayInfo = reactive({
  fabricName: 'xxx坯布编号#坯布名称', // 坯布名称
  productionNotice: 'xxxxxxx', // 生产通知单
  rollNo: '10', // 卷号
  productionOrder: 'xxxxxxxxxxx', // 排产单号
  dailyOutput: '32条', // 本日产量
  monthlyOutput: '333条', // 本月产量
  yarnName: 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx', // 纱名
})

// 电子秤相关
const measureWeightStore = useMeasureWeightStore()
watch(() => measureWeightStore.measureWeightState.currentFrameData, (newValue: number) => {
  formData.weighing = newValue
})
const isWeightConnected = computed(() => {
  return measureWeightStore.measureWeightState.isConnected
})
const WeightLog = computed(() => {
  return measureWeightStore.measureWeightState.Log
})
const showWeightLog = ref(false)
function toggleWeightLog() {
  showWeightLog.value = !showWeightLog.value
}

// 码表相关
const measureMeterStore = useMeasureMeterStore()
const isConnected = computed(() => {
  return measureMeterStore.measureMeterState.isConnected
})
const Log = computed(() => {
  return measureMeterStore.measureMeterState.Log
})
const showLog = ref(false)
function toggleLog() {
  showLog.value = !showLog.value
}
// 弹窗控制
const showElectronicScaleModal = ref(false)

// 疵点信息弹框引用
const defectInfoDialogRef = ref()

// 疵点记录弹框引用
const defectRecordsDialogRef = ref()

// 电子秤设置
const electronicScaleSettings = reactive({
  connection: '链接',
  stableValue: 15,
  isStable: false,
  autoSave: true,
  autoSaveSeconds: 3,
  noWeaverSelection: false,
  noInspectorSelection: false,
  weightReflection: false,
  dataHead: '',
  dataEnd: '',
  scanCodeReading: false,
  portAutoOpen: false,
  dataLength: '',
})

// 全局配置相关
const { fetchData: getGlobalConfig, data: globalConfigData, success: globalConfigSuccess, msg: globalConfigMsg } = GetGlobalConfigDropdownList()
const { fetchData: saveConfig } = BatchUpdateGlobalConfigList()

// 配置ID映射 - 使用enum
const configIdMap = {
  autoSaveSeconds: GlobalEnum.AutoSaveSeconds, // 设置延后多少秒保存
  noWeaverSelection: GlobalEnum.NoWeaverSelection, // 是否无需选择织工
  noInspectorSelection: GlobalEnum.NoInspectorSelection, // 是否无需选择查布
  weightReflection: GlobalEnum.WeightReflection, // 是否重量反转
  dataHead: GlobalEnum.DataHead, // 数据头 文本
  dataEnd: GlobalEnum.DataEnd, // 数据尾 文本
  scanCodeReading: GlobalEnum.ScanCodeReading, // 是否扫码后读数
  stableValue: GlobalEnum.StableValue, // 设置稳定值 文本
  autoSave: GlobalEnum.AutoSave, // 是否自动保存
}

// 获取设置数据
async function getSettingsData() {
  const ids = Object.values(configIdMap).join(',')
  await getGlobalConfig({
    ids,
  })

  // 将获取到的配置应用到设置中
  if (globalConfigSuccess.value)
    applyGlobalConfigToSettings()
  else
    ElMessage.error(globalConfigMsg.value)
}

// 将全局配置应用到电子秤设置
function applyGlobalConfigToSettings() {
  if (!globalConfigData.value)
    return

  globalConfigData.value?.list?.forEach((config: any) => {
    const configId = config.id
    switch (configId) {
      case configIdMap.autoSaveSeconds:
        electronicScaleSettings.autoSaveSeconds = Number(config.options) || 3
        break
      case configIdMap.noWeaverSelection:
        electronicScaleSettings.noWeaverSelection = config.options === 'true' || config.options === true
        break
      case configIdMap.noInspectorSelection:
        electronicScaleSettings.noInspectorSelection = config.options === 'true' || config.options === true
        break
      case configIdMap.weightReflection:
        electronicScaleSettings.weightReflection = config.options === 'true' || config.options === true
        break
      case configIdMap.dataHead:
        electronicScaleSettings.dataHead = config.options || ''
        break
      case configIdMap.dataEnd:
        electronicScaleSettings.dataEnd = config.options || ''
        break
      case configIdMap.scanCodeReading:
        electronicScaleSettings.scanCodeReading = config.options === 'true' || config.options === true
        break
      case configIdMap.stableValue:
        electronicScaleSettings.stableValue = Number(config.options) || 0
        electronicScaleSettings.isStable = !!Number(config.options)
        break
      case configIdMap.autoSave:
        electronicScaleSettings.autoSave = config.options === 'true' || config.options === true
        break
    }
  })
}

// 保存设置到全局配置
async function saveSettingsToGlobalConfig() {
  const configsToSave = [
    { id: configIdMap.autoSaveSeconds, options: electronicScaleSettings.autoSaveSeconds.toString() },
    { id: configIdMap.noWeaverSelection, options: electronicScaleSettings.noWeaverSelection.toString() },
    { id: configIdMap.noInspectorSelection, options: electronicScaleSettings.noInspectorSelection.toString() },
    { id: configIdMap.weightReflection, options: electronicScaleSettings.weightReflection.toString() },
    { id: configIdMap.dataHead, options: electronicScaleSettings.dataHead },
    { id: configIdMap.dataEnd, options: electronicScaleSettings.dataEnd },
    { id: configIdMap.scanCodeReading, options: electronicScaleSettings.scanCodeReading.toString() },
    { id: configIdMap.stableValue, options: electronicScaleSettings.stableValue.toString() },
    { id: configIdMap.autoSave, options: electronicScaleSettings.autoSave.toString() },
  ]

  try {
    await saveConfig({
      update_global_config_list: configsToSave.map((item) => {
        return {
          id: item.id,
          options: item.options,
        }
      }),
    })
    ElMessage.success('设置保存成功')
  }
  catch (error) {
    ElMessage.error('设置保存失败')
    console.error('保存设置失败:', error)
  }
}

const ruleFormRef = ref<FormInstance>()
const rules = computed(() => ({
  barcode: [
    { required: true, message: '请输入条码', trigger: 'change' },
  ],
  weaver: [
    { required: !electronicScaleSettings.noWeaverSelection, message: '请选择织工', trigger: 'blur' },
  ],
  inspector: [
    { required: !electronicScaleSettings.noInspectorSelection, message: '请选择查布', trigger: 'blur' },
  ],
}))
// 连接串口设备
function handleConnectToSerialPort() {
  if (!isConnected.value) {
    // 准备数据头尾配置
    const delimiterConfig = {
      dataHeader: electronicScaleSettings.dataHead
        ? Array.from(electronicScaleSettings.dataHead).map(char => char.charCodeAt(0))
        : [],
      dataFooter: electronicScaleSettings.dataEnd
        ? Array.from(electronicScaleSettings.dataEnd).map(char => char.charCodeAt(0))
        : [13, 10], // 默认 \r\n
      useCustomDelimiter: !!(electronicScaleSettings.dataHead || electronicScaleSettings.dataEnd),
    }

    // 准备稳定性配置
    const stabilityConfig = {
      enabled: electronicScaleSettings.isStable,
      stableCount: electronicScaleSettings.stableValue || 3,
      precision: 2, // 默认精度到小数点后2位
    }

    const measureWeightService = useMeasureWeightService(delimiterConfig, stabilityConfig)
    try {
      measureWeightService.connectToSerialPort()
      measureWeightStore.setMeasureWeightState(measureWeightService)
    }
    catch (error) {
      console.error('串口连接错误:', error)
    }
  }
}

// 断开电子秤串口连接
function handleDisconnectToWeightSerialPort() {
  measureWeightStore.measureWeightState.clearLogMessages()
  measureWeightStore.clearLogList()
  measureWeightStore.measureWeightState.disconnectPort()
}

// 连接码表串口设备
function handleConnectToMeterSerialPort() {
  if (!isConnected.value) {
    const measureMeterService = useMeasureMeterService()
    try {
      measureMeterService.connectToSerialPort()
      measureMeterStore.setMeasureMeterState(measureMeterService)
    }
    catch (error) {
      console.error('码表串口连接错误:', error)
    }
  }
}

// 断开码表串口连接
function handleDisconnectToSerialPort() {
  measureMeterStore.measureMeterState.clearLogMessages()
  measureMeterStore.clearLogList()
  measureMeterStore.measureMeterState.disconnectPort()
}

// 码表清零
function handleZero() {
  measureMeterStore.measureMeterState.handleZero()
}

// 监听码表数据变化
const currentFrameData = computed(() => measureMeterStore.measureMeterState.currentFrameData)

// 监听码表数据变化，实时更新疵点位置
watch(() => currentFrameData.value, (newVal) => {
  // 如果疵点弹框是打开状态，实时更新疵点位置
  if (defectInfoDialogRef.value?.state.showModal && newVal)
    defectInfoDialogRef.value.setDefectPosition(newVal)
})

// 自动保存 - 移除独立的autoSave，使用electronicScaleSettings.autoSave

// 当前选中的tab
const activeTab = ref('allDefects')
const { fetchData: fetchList, data: resList, success: fetchSuccess, loading: defectTypesLoading } = getInfoBasicDefectlistEnum()
// 疵点数据 - 从API获取
const defectTypes = ref<Array<{ id?: number, name: string, count: number }>>([])

// 获取骨架屏按钮宽度
function getSkeletonWidth(index: number) {
  const widths = ['80px', '100px', '120px', '90px', '110px', '85px']
  return widths[index % widths.length]
}

// 加载疵点列表
async function loadDefectTypes() {
  await fetchList()
  if (fetchSuccess.value) {
    const data = resList?.value.list || []
    // 将API数据转换为组件需要的格式，只取状态为启用的疵点
    const apiDefects = data
      .filter((item: Api.DefectBasicInfo.Response) => item.status === 1) // 只取启用状态的疵点
      .map((item: Api.DefectBasicInfo.Response) => ({
        ...item,
        id: item.id,
        name: item.name || '未知疵点',
        count: 0,
      }))

    defectTypes.value = apiDefects

    // 确保"其他"选项存在
    const hasOther = defectTypes.value.some(item => item.name === '其他')
    if (!hasOther)
      defectTypes.value.push({ name: '其他', count: 0 })
  }
  else {
    throw new Error('API返回数据格式不正确')
  }
}

// 计算总疵点数
const totalDefects = computed(() => {
  return defectTypes.value.reduce((total, item) => total + item.count, 0)
})

// 详细疵点统计数据 - 记录每个疵点的详细信息
const defectStatistics = ref<Array<{
  id: string
  name: string
  barcode: string
  position: number
  count: number
  score: number
  timestamp: string
}>>([])

// 计算疵点统计显示数据
const defectStatisticsDisplay = computed(() => {
  const stats = new Map<string, { count: number, totalScore: number }>()

  // 统计每种疵点的数量和总分数
  defectStatistics.value.forEach((item) => {
    const key = item.name
    if (stats.has(key)) {
      const existing = stats.get(key)!
      existing.count += item.count
      existing.totalScore += item.score * item.count
    }
    else {
      stats.set(key, { count: item.count, totalScore: item.score * item.count })
    }
  })

  // 转换为显示格式
  return Array.from(stats.entries()).map(([name, data]) => ({
    name,
    count: data.count,
    totalScore: data.totalScore,
    displayText: `${name}(${data.count})`,
  }))
})

// 选择织工
function selectWeaver() {
  // TODO: 实现选择织工逻辑
  ElMessage.info('选择织工功能待实现')
}

// 选择查布
function selectInspector() {
  // TODO: 实现选择查布逻辑
  ElMessage.info('选择查布功能待实现')
}

// 保存
async function save() {
  if (!ruleFormRef.value)
    return

  try {
    await ruleFormRef.value.validate()

    // TODO: 实现保存逻辑
    ElMessage.success('保存成功')
  }
  catch (error) {
    ElMessage.error('请完善表单信息')
  }
}

// 清空
function clear() {
  ElMessageBox.confirm('确定要清空所有数据吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    Object.assign(formData, {
      grade: '',
      barcode: '',
      machineNo: '',
      weaver: '',
      inspector: '',
      weighing: 0,
      actualWeight: 0,
    })
    defectTypes.value.forEach(item => item.count = 0)
    defectStatistics.value = []
    ElMessage.success('清空成功')
  })
}

// 设置电子秤
function openElectronicScaleModal() {
  showElectronicScaleModal.value = true
}

async function saveElectronicScaleSettings(settings?: any) {
  // 如果传入了设置参数，更新本地设置
  if (settings)
    Object.assign(electronicScaleSettings, settings)

  // 保存到全局配置
  await saveSettingsToGlobalConfig()

  // 如果已经连接，更新现有连接的配置
  if (isConnected.value && measureWeightStore.measureWeightState) {
    // 更新数据头尾配置
    const delimiterConfig = {
      dataHeader: electronicScaleSettings.dataHead
        ? Array.from(electronicScaleSettings.dataHead).map((char: string) => char.charCodeAt(0))
        : [],
      dataFooter: electronicScaleSettings.dataEnd
        ? Array.from(electronicScaleSettings.dataEnd).map((char: string) => char.charCodeAt(0))
        : [13, 10], // 默认 \r\n
      useCustomDelimiter: !!(electronicScaleSettings.dataHead || electronicScaleSettings.dataEnd),
    }

    // 更新稳定性配置
    const stabilityConfig = {
      enabled: electronicScaleSettings.isStable,
      stableCount: electronicScaleSettings.stableValue || 3,
      precision: 2, // 默认精度到小数点后2位
    }

    // 更新现有连接的配置
    if (measureWeightStore.measureWeightState.updateDelimiterConfig)
      measureWeightStore.measureWeightState.updateDelimiterConfig(delimiterConfig)

    if (measureWeightStore.measureWeightState.updateStabilityConfig)
      measureWeightStore.measureWeightState.updateStabilityConfig(stabilityConfig)

    ElMessage.success('电子秤设置已更新并应用到当前连接')
  }

  // 如果是从组件保存，不需要关闭弹窗（组件会自己关闭）
  if (!settings)
    showElectronicScaleModal.value = false

  ElMessage.success('电子秤设置保存成功')
}

// 点击疵点
function clickDefect(defect: any) {
  // 设置码表命令为读取上排数值
  measureMeterStore.setCommand(readUpperNumberCommand)
  // 开始码表计时
  if (isConnected.value)
    measureMeterStore.measureMeterState.resume()

  // 打开疵点信息弹框
  const isOther = defect.name === '其他'
  defectInfoDialogRef.value?.showAddDialog(defect, isOther, true)

  // 将当前码表数值设置到疵点位置
  if (defectInfoDialogRef.value && currentFrameData.value)
    defectInfoDialogRef.value.setDefectPosition(currentFrameData.value)
}

// 疵点弹框关闭时暂停码表
function handleDefectDialogClose() {
  // 暂停码表计时
  if (isConnected.value)
    measureMeterStore.measureMeterState.pause()
}

// 处理疵点信息确认
function handleDefectSure(defectData: any) {
  // 更新疵点数量
  const defectType = defectTypes.value.find(item => item.name === defectData.defect_name || item.name === defectData.name)
  if (defectType)
    defectType.count += defectData.defect_count

  // 添加到详细统计数据
  const statisticItem = {
    id: `defect_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
    name: defectData.defect_name || defectData.name,
    barcode: formData.barcode || '000111782349', // 从表单获取条形码
    position: defectData.defect_position || 0,
    count: defectData.defect_count,
    score: defectData.score,
    timestamp: new Date().toLocaleString(),
  }
  defectStatistics.value.push(statisticItem)

  ElMessage.success('疵点信息添加成功')
}

// 点击疵点统计项
function handleStatItemClick(defectName: string) {
  if (defectName === 'all') {
    // 显示疵点记录弹框
    defectRecordsDialogRef.value?.showDialog('', defectStatistics.value)
    return
  }
  // 过滤出该疵点类型的所有记录
  const records = defectStatistics.value.filter(record => record.name === defectName)
  if (records.length === 0) {
    ElMessage.info(`暂无${defectName}的记录`)
    return
  }

  // 显示疵点记录弹框
  defectRecordsDialogRef.value?.showDialog(defectName, defectStatistics.value)
}

// 处理疵点记录更新
function handleDefectRecordUpdate(editedData: any) {
  // 更新详细统计数据中的记录
  const index = defectStatistics.value.findIndex(item => item.id === editedData.originalId)
  if (index > -1) {
    const oldRecord = defectStatistics.value[index]

    // 更新记录
    defectStatistics.value[index] = {
      ...oldRecord,
      name: editedData.defect_name || editedData.name,
      position: editedData.defect_position || 0,
      count: editedData.defect_count,
      score: editedData.score,
      timestamp: new Date().toLocaleString(), // 更新时间戳
    }

    // 如果疵点名称发生变化，需要更新疵点类型计数
    if (oldRecord.name !== editedData.defect_name) {
      // 从旧疵点类型中减去数量
      const oldDefectType = defectTypes.value.find(item => item.name === oldRecord.name)
      if (oldDefectType && oldDefectType.count >= oldRecord.count)
        oldDefectType.count -= oldRecord.count

      // 向新疵点类型中添加数量
      const newDefectType = defectTypes.value.find(item => item.name === editedData.defect_name)
      if (newDefectType)
        newDefectType.count += editedData.defect_count
    }
    else {
      // 疵点名称未变化，只更新数量差异
      const defectType = defectTypes.value.find(item => item.name === oldRecord.name)
      if (defectType) {
        const countDiff = editedData.defect_count - oldRecord.count
        defectType.count += countDiff
        // 确保计数不为负数
        if (defectType.count < 0)
          defectType.count = 0
      }
    }

    ElMessage.success('疵点记录更新成功')
  }
}

// 处理疵点记录删除
function handleDefectRecordDelete(record: any) {
  // 从详细统计数据中移除
  const index = defectStatistics.value.findIndex(item => item.id === record.id)
  if (index > -1) {
    const deletedRecord = defectStatistics.value[index]
    defectStatistics.value.splice(index, 1)

    // 更新疵点类型计数
    const defectType = defectTypes.value.find(item => item.name === deletedRecord.name)
    if (defectType && defectType.count >= deletedRecord.count)
      defectType.count -= deletedRecord.count
  }
}

// 键盘事件
function handleKeydown(event: KeyboardEvent) {
  if (event.key === 'F4') {
    event.preventDefault()
    save()
  }
  else if (event.key === 'F1') {
    event.preventDefault()
    clear()
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
  // 加载疵点列表
  loadDefectTypes()
  // 加载电子秤设置
  getSettingsData()
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<template>
  <div class="grey-cloth-inspection">
    <el-row :gutter="10" class="main-content">
      <!-- 左侧表单区域 -->
      <el-col :span="12" class="left-panel">
        <el-card class="form-section" body-class="h-full flex flex-col">
          <div class="flex-1 overflow-y-scroll overflow-x-hidden mb-2">
            <h3 class="section-title">
              质检查布
            </h3>

            <!-- 表单区域 -->
            <el-form ref="ruleFormRef" :rules="rules" size="large" :model="formData" label-width="80px" class="inspection-form">
              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="条码:" size="large" prop="barcode">
                    <el-input v-model="formData.barcode" placeholder="请输入条码" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="等级:">
                    <el-select v-model="formData.grade" size="large" placeholder="请选择等级" style="width: 100%">
                      <el-option label="单独用一个字典维护" value="single" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="机号:" size="large">
                    <el-input v-model="formData.machineNo" placeholder="请输入机号" />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="16">
                <el-col :span="24">
                  <el-form-item label="织工:" size="large" prop="weaver" :required="!electronicScaleSettings.noWeaverSelection">
                    <div class="input-with-button">
                      <el-input v-model="formData.weaver" style="width: 100%" placeholder="请选择织工" readonly />
                      <el-button type="primary" @click="selectWeaver">
                        选择织工(F4)
                      </el-button>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="16">
                <el-col :span="24">
                  <el-form-item label="查布:" prop="inspector" :required="!electronicScaleSettings.noInspectorSelection">
                    <div class="input-with-button">
                      <el-input v-model="formData.inspector" placeholder="请选择查布" readonly />
                      <el-button type="primary" @click="selectInspector">
                        选择查布(F3)
                      </el-button>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="16">
                <el-col :span="8">
                  <el-form-item label="称重:">
                    <el-input-number
                      v-model="formData.weighing"
                      :precision="2"
                      :step="0.01"
                      style="width: 100%"
                      placeholder="称重"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="实重:">
                    <el-input-number
                      v-model="formData.actualWeight"
                      :precision="2"
                      :step="0.01"
                      style="width: 100%"
                      placeholder="实重"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>

            <!-- 显示信息区域 -->
            <div class="info-display">
              <el-row :gutter="10" class="info-row">
                <el-col :span="12">
                  <div class="info-item">
                    <span class="info-label">坯布名称</span>
                    <span class="info-value">{{ displayInfo.fabricName }}</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="info-item">
                    <span class="info-label">生产通知单</span>
                    <span class="info-value">{{ displayInfo.productionNotice }}</span>
                  </div>
                </el-col>
              </el-row>

              <el-row :gutter="10" class="info-row">
                <el-col :span="12">
                  <div class="info-item">
                    <span class="info-label">卷号</span>
                    <span class="info-value">{{ displayInfo.rollNo }}</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="info-item">
                    <span class="info-label">排产单号</span>
                    <span class="info-value">{{ displayInfo.productionOrder }}</span>
                  </div>
                </el-col>
              </el-row>

              <el-row :gutter="10" class="info-row">
                <el-col :span="12">
                  <div class="info-item">
                    <span class="info-label">本日产量</span>
                    <span class="info-value">{{ displayInfo.dailyOutput }}</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="info-item">
                    <span class="info-label">本月产量</span>
                    <span class="info-value">{{ displayInfo.monthlyOutput }}</span>
                  </div>
                </el-col>
              </el-row>

              <div class="yarn-info">
                <span class="info-label">纱名</span>
                <div class="yarn-content">
                  {{ displayInfo.yarnName }}
                </div>
              </div>
            </div>
          </div>

          <!-- 底部操作区域 -->
          <div class="bottom-actions">
            <div class="mb-2">
              要求布重：
            </div>
            <div class="action-buttons flex justify-between">
              <el-space>
                <el-button size="large" type="primary" @click="save">
                  保存(F4)
                </el-button>
                <el-button size="large" @click="clear">
                  清空(F1)
                </el-button>
              </el-space>
              <el-space>
                <el-checkbox v-model="electronicScaleSettings.autoSave" size="large">
                  自动保存
                </el-checkbox>
                <el-button size="large" @click="openElectronicScaleModal">
                  设置
                </el-button>
              </el-space>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧疵点选择区域 -->
      <el-col :span="12" class="right-panel">
        <el-card class="defect-section" body-class="h-full flex flex-col">
          <el-tabs v-model="activeTab" size="large" class="defect-tabs" tab-position="top">
            <!-- 全部疵点 -->
            <el-tab-pane label="全部疵点" name="allDefects">
              <div class="defect-buttons">
                <!-- 骨架屏 -->
                <el-skeleton :loading="defectTypesLoading">
                  <template #template>
                    <div class="defect-skeleton">
                      <el-skeleton-item
                        v-for="i in 16"
                        :key="i"
                        variant="button"
                        :style="{
                          width: getSkeletonWidth(i),
                          height: '40px',
                          marginRight: '10px',
                          marginBottom: '10px',
                        }"
                      />
                    </div>
                  </template>
                  <template #default>
                    <el-button
                      v-for="defect in defectTypes"
                      :key="defect.name"
                      class="defect-btn" :class="[{ selected: defect.count > 0 }]"
                      @click="clickDefect(defect)"
                    >
                      {{ defect.name }}
                      <span v-if="defect.count > 0" class="defect-count">({{ defect.count }})</span>
                    </el-button>
                  </template>
                </el-skeleton>
              </div>
            </el-tab-pane>

            <!-- 前处理 -->
            <el-tab-pane label="前处理" name="pretreatment">
              <div class="tab-content">
                前处理相关内容
              </div>
            </el-tab-pane>

            <!-- 定性 -->
            <el-tab-pane label="定性" name="qualitative">
              <div class="tab-content">
                定性相关内容
              </div>
            </el-tab-pane>

            <!-- 织造 -->
            <el-tab-pane label="织造" name="weaving">
              <div class="tab-content">
                织造相关内容
              </div>
            </el-tab-pane>
          </el-tabs>

          <!-- 疵点统计 -->
          <div class="defect-statistics">
            <el-scrollbar>
              <div class="statistics-content">
                <span class="stat-item" @click="handleStatItemClick('all')">总疵点 ({{ totalDefects }})</span>
                <!-- <span class="stat-item">疵点种类 ({{ selectedDefects }})</span> -->
                <span
                  v-for="item in defectStatisticsDisplay"
                  :key="item.name"
                  class="stat-item clickable"
                  @click="handleStatItemClick(item.name)"
                >
                  {{ item.displayText }}
                </span>
                <span v-if="defectStatisticsDisplay.length === 0" class="stat-item no-data">
                  暂无疵点记录
                </span>
              </div>
            </el-scrollbar>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 设置电子秤弹窗 -->
    <ElectronicScaleSettings
      v-model="showElectronicScaleModal"
      :settings="electronicScaleSettings"
      :show-meter-connection="true"
      :show-inspector-option="true"
      :is-weight-connected="isWeightConnected"
      :is-meter-connected="isConnected"
      :show-weight-log="showWeightLog"
      :show-meter-log="showLog"
      @save="saveElectronicScaleSettings"
      @connect-weight="handleConnectToSerialPort"
      @disconnect-weight="handleDisconnectToWeightSerialPort"
      @connect-meter="handleConnectToMeterSerialPort"
      @disconnect-meter="handleDisconnectToSerialPort"
      @toggle-weight-log="toggleWeightLog"
      @toggle-meter-log="toggleLog"
      @meter-zero="handleZero"
    >
      <template #weight-log>
        <WeightLog v-if="showWeightLog" />
      </template>
      <template #meter-log>
        <Log v-if="showLog" />
      </template>
    </ElectronicScaleSettings>

    <!-- 疵点信息弹框 -->
    <DefectInfoDialog
      ref="defectInfoDialogRef"
      @handle-sure="handleDefectSure"
      @handle-hide="handleDefectDialogClose"
    />

    <!-- 疵点记录弹框 -->
    <DefectRecordsDialog
      ref="defectRecordsDialogRef"
      @on-update="handleDefectRecordUpdate"
      @on-delete="handleDefectRecordDelete"
    />
  </div>
</template>

<style lang="scss" scoped>
.grey-cloth-inspection {
  overflow: hidden;
  height: 100%;

  .main-content {
    height: 100%;
  }

  .left-panel {
    height: 100%;
    padding-right: 20px;

    .form-section {
      height: 100%;
      display: flex;
      flex-direction: column;

      .section-title {
        font-size: 18px;
        font-weight: bold;
        color: #303133;
      }

      .inspection-form {
        flex-shrink: 0;

        .input-with-button {
          display: flex;
          gap: 10px;
          width: 100%;

          .el-input {
            flex: 1;
          }

          .el-button {
            flex-shrink: 0;
          }
        }
      }

      .info-display {
        flex: 1;
        border-radius: 4px;
        // margin-bottom: 20px;

        .info-row {
          margin-bottom: 10px;
        }

        .info-item {
          padding: 10px;
          border-radius: 10px;
          background: #f5f7fa;

          .info-label {
            display: block;
            color: #606266;
            font-size: 18px;
            flex-shrink: 0;
          }

          .info-value {
            color: #303133;
            font-size: 18px;
            flex: 1;
          }
        }

        .yarn-info {
          margin-top: 10px;
          border-radius: 10px;
          background: #f5f7fa;
          padding: 10px;

          .info-label {
            display: block;
            color: #606266;
            font-size: 18px;
          }

          .yarn-content {
            color: #303133;
            font-size: 18px;
            word-break: break-all;
          }
        }
      }

      .bottom-actions {
        flex-shrink: 0;
        border-top: 1px solid #e4e7ed;
        padding-top: 15px;

        .action-buttons {
          display: flex;
          align-items: center;
          gap: 15px;
        }
      }
    }
  }

  .right-panel {
    height: 100%;
    padding-left: 20px;

    .defect-section {
      height: 100%;
      display: flex;
      flex-direction: column;

      .defect-tabs {
        flex: 1;
        display: flex;

        .defect-buttons {
          display: flex;
          flex-wrap: wrap;
          gap: 10px;
          :deep(.el-button+.el-button){
            margin: 0
          }
          .defect-skeleton {
            display: flex;
            flex-wrap: wrap;
            width: 100%;
          }

          .defect-btn {
            min-width: 100px;
            height: 40px;
            border: 1px solid #dcdfe6;
            background: white;
            color: #606266;
            border-radius: 4px;
            transition: all 0.3s;

            &:hover {
              border-color: #409eff;
              color: #409eff;
            }

            &.selected {
              background: #409eff;
              border-color: #409eff;
              color: white;
            }

            .defect-count {
              margin-left: 5px;
              font-weight: bold;
            }
          }
        }

        .tab-content {
          padding: 20px;
          text-align: center;
          color: #909399;
        }
      }

      .defect-statistics {
        flex-shrink: 0;
        border-top: 1px solid #e4e7ed;
        padding: 15px 0;

        :deep(.el-scrollbar__wrap) {
          overflow-x: auto;
          overflow-y: hidden;
        }

        :deep(.el-scrollbar__view) {
          display: flex;
          align-items: center;
        }

        .statistics-content {
          display: flex;
          gap: 20px;
          padding-bottom: 5px;

          .stat-item {
            flex-shrink: 0;
            padding: 8px 15px;
            background: #f0f9ff;
            border: 1px solid #b3d8ff;
            border-radius: 4px;
            color: #409eff;
            font-size: 14px;
            white-space: nowrap;
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
              background: #e6f7ff;
              border-color: #91d5ff;
              transform: translateY(-1px);
            }

            &.clickable {
              cursor: pointer;

              &:hover {
                background: #e6f7ff;
                border-color: #91d5ff;
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
              }
            }

            &.no-data {
              background: #f5f5f5;
              border-color: #d9d9d9;
              color: #999;
              font-style: italic;
              cursor: default;

              &:hover {
                background: #f5f5f5;
                border-color: #d9d9d9;
                transform: none;
              }
            }
          }
        }
      }
    }
  }

  // 弹窗样式
  .electronic-scale-modal {
    // padding: 20px;

    .stable-value-row {
      display: flex;
      align-items: center;
    }

    .hint-text {
      color: #909399;
      font-size: 12px;
    }

    .el-form-item {
      margin-bottom: 20px;

      .el-radio {
        margin-right: 15px;
      }
    }
  }

  // 美化提示样式
  .setting-label-container {
    display: flex;
    align-items: center;
    gap: 8px;

    .info-badge {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 18px;
      height: 18px;
      background: linear-gradient(135deg, #409eff, #66b1ff);
      border-radius: 50%;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        transform: scale(1.1);
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.4);
      }

      .info-icon {
        color: white;
        font-size: 12px;
      }
    }
  }

  .custom-tooltip-content {
    .tooltip-title {
      font-weight: 600;
      font-size: 14px;
      color: #fff;
      margin-bottom: 6px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);
      padding-bottom: 4px;
    }

    .tooltip-desc {
      font-size: 13px;
      color: rgba(255, 255, 255, 0.9);
      line-height: 1.4;
    }
  }

  .stable-value-container {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .input-group {
      display: flex;
      align-items: center;
      gap: 12px;

      .stable-checkbox {
        flex-shrink: 0;
      }

      .stable-input {
        width: 200px;

        .input-suffix {
          color: #909399;
          font-size: 12px;
        }
      }
    }

    .setting-hint {
      display: flex;
      align-items: flex-start;
      gap: 6px;
      padding: 8px 12px;
      background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
      border: 1px solid #bae6fd;
      border-radius: 6px;

      .hint-icon {
        color: #0ea5e9;
        font-size: 14px;
        margin-top: 1px;
        flex-shrink: 0;
      }

      .hint-text {
        font-size: 12px;
        color: #0369a1;
        line-height: 1.4;
      }
    }
  }

  .data-setting-container {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .data-input {
      width: 300px;
    }

    .setting-note {
      font-size: 12px;
      color: #909399;
      padding-left: 4px;
    }
  }

}

// 全局样式调整
:deep(.el-input-number) {
  width: 100%;

  .el-input__inner {
    text-align: left;
  }
}
</style>
