<script setup lang="ts">
import { computed, reactive, watch } from 'vue'
import { InfoFilled } from '@element-plus/icons-vue'

interface ElectronicScaleConfig {
  stableValue: number
  isStable: boolean
  autoSave: boolean
  autoSaveSeconds: number
  noWeaverSelection: boolean
  noInspectorSelection: boolean
  weightReflection: boolean
  dataHead: string
  dataEnd: string
  scanCodeReading: boolean
  portAutoOpen: boolean
}

interface Props {
  modelValue: boolean
  settings: ElectronicScaleConfig
  showMeterConnection?: boolean // 是否显示码表连接
  showInspectorOption?: boolean // 是否显示查布选项
  isWeightConnected?: boolean
  isMeterConnected?: boolean
  showWeightLog?: boolean
  showMeterLog?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showMeterConnection: false,
  showInspectorOption: false,
  isWeightConnected: false,
  isMeterConnected: false,
  showWeightLog: false,
  showMeterLog: false,
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'save': [settings: ElectronicScaleConfig]
  'connect-weight': []
  'disconnect-weight': []
  'connect-meter': []
  'disconnect-meter': []
  'toggle-weight-log': []
  'toggle-meter-log': []
  'meter-zero': []
}>()

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

// 本地设置副本
const localSettings = reactive<ElectronicScaleConfig>({ ...props.settings })

// 监听外部设置变化
watch(() => props.settings, (newSettings) => {
  Object.assign(localSettings, newSettings)
}, { deep: true })

function handleSave() {
  emit('save', { ...localSettings })
  visible.value = false
}

function handleConnectWeight() {
  emit('connect-weight')
}

function handleDisconnectWeight() {
  emit('disconnect-weight')
}

function handleConnectMeter() {
  emit('connect-meter')
}

function handleDisconnectMeter() {
  emit('disconnect-meter')
}

function handleToggleWeightLog() {
  emit('toggle-weight-log')
}

function handleToggleMeterLog() {
  emit('toggle-meter-log')
}

function handleMeterZero() {
  emit('meter-zero')
}
</script>

<template>
  <vxe-modal
    v-model="visible"
    title="设置电子秤"
    width="800"
    show-footer
    resize
    show-close
  >
    <div class="electronic-scale-modal">
      <el-form size="large" :model="localSettings" label-width="100px">
        <!-- 电子秤连接 -->
        <el-form-item label="电子秤:">
          <el-button @click="handleConnectWeight">
            连接({{ isWeightConnected ? '已连接✅' : '未连接❌' }})
          </el-button>
          <el-button @click="handleDisconnectWeight">
            断开
          </el-button>
          <el-button class="ml-2" @click="handleToggleWeightLog">
            日志
          </el-button>
          <slot name="weight-log" />
        </el-form-item>

        <!-- 码表连接（可选） -->
        <el-form-item v-if="showMeterConnection" label="码表:">
          <el-button @click="handleConnectMeter">
            连接({{ isMeterConnected ? '已连接✅' : '未连接❌' }})
          </el-button>
          <el-button @click="handleDisconnectMeter">
            断开
          </el-button>
          <el-button @click="handleMeterZero">
            清零
          </el-button>
          <el-button class="ml-2" @click="handleToggleMeterLog">
            日志
          </el-button>
          <slot name="meter-log" />
        </el-form-item>

        <!-- 稳定值设置 -->
        <el-form-item label="稳定值:">
          <template #label>
            <div class="setting-label-container">
              <span>稳定设置</span>
              <el-tooltip effect="dark" placement="top" :show-after="300">
                <template #content>
                  <div class="custom-tooltip-content">
                    <div class="tooltip-title">稳定值说明</div>
                    <div class="tooltip-desc">
                      设置电子台秤连续稳定的重量值次数<br>
                      用于判断重量读数是否稳定
                    </div>
                  </div>
                </template>
                <div class="info-badge">
                  <el-icon class="info-icon">
                    <InfoFilled />
                  </el-icon>
                </div>
              </el-tooltip>
            </div>
          </template>
          <div class="stable-value-container">
            <div class="input-group">
              <el-checkbox v-model="localSettings.isStable" class="stable-checkbox">
                启用稳定值检测
              </el-checkbox>
              <el-input-number 
                v-if="localSettings.isStable" 
                v-model="localSettings.stableValue" 
                :precision="0" 
                :min="1" 
                class="stable-input"
              >
                <template #suffix>
                  <span class="input-suffix">次</span>
                </template>
              </el-input-number>
            </div>
            <div class="setting-hint">
              <el-icon class="hint-icon"><InfoFilled /></el-icon>
              <span class="hint-text">输入 0 则使用硬件的稳定机制判断电子秤读数是否稳定</span>
            </div>
          </div>
        </el-form-item>

        <!-- 自动保存设置 -->
        <el-form-item label="保存设置">
          <el-checkbox v-model="localSettings.autoSave">
            自动保存（多少秒自动保存）
          </el-checkbox>
          <el-input-number 
            v-if="localSettings.autoSave" 
            v-model="localSettings.autoSaveSeconds" 
            :min="0" 
            style="width: 200px;"
          >
            <template #suffix>
              <span>秒</span>
            </template>
          </el-input-number>
        </el-form-item>

        <!-- 选项设置 -->
        <el-row>
          <el-col :span="8">
            <el-form-item>
              <el-checkbox v-model="localSettings.noWeaverSelection" label="不需选择织工" />
            </el-form-item>
          </el-col>
          <el-col v-if="showInspectorOption" :span="8">
            <el-form-item>
              <el-checkbox v-model="localSettings.noInspectorSelection" label="不需选择查布" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item>
              <el-checkbox v-model="localSettings.weightReflection" label="重量反转" />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 数据头设置 -->
        <el-form-item>
          <template #label>
            <div class="setting-label-container">
              <span>数据头</span>
              <el-tooltip effect="dark" placement="top" :show-after="300">
                <template #content>
                  <div class="custom-tooltip-content">
                    <div class="tooltip-title">数据头设置</div>
                    <div class="tooltip-desc">
                      用于区分串口数据的开头标识<br>
                      一般情况下不需要填写
                    </div>
                  </div>
                </template>
                <div class="info-badge">
                  <el-icon class="info-icon">
                    <InfoFilled />
                  </el-icon>
                </div>
              </el-tooltip>
            </div>
          </template>
          <div class="data-setting-container">
            <el-input 
              v-model="localSettings.dataHead" 
              placeholder="数据头（可选）" 
              class="data-input"
              clearable
            />
            <div class="setting-note">可选配置，用于标识数据包开始</div>
          </div>
        </el-form-item>

        <!-- 数据尾设置 -->
        <el-form-item>
          <template #label>
            <div class="setting-label-container">
              <span>数据尾</span>
              <el-tooltip effect="dark" placement="top" :show-after="300">
                <template #content>
                  <div class="custom-tooltip-content">
                    <div class="tooltip-title">数据尾设置</div>
                    <div class="tooltip-desc">
                      用于区分串口数据的结尾标识<br>
                      一般情况下不需要填写
                    </div>
                  </div>
                </template>
                <div class="info-badge">
                  <el-icon class="info-icon">
                    <InfoFilled />
                  </el-icon>
                </div>
              </el-tooltip>
            </div>
          </template>
          <div class="data-setting-container">
            <el-input 
              v-model="localSettings.dataEnd" 
              placeholder="数据尾（可选）" 
              class="data-input"
              clearable
            />
            <div class="setting-note">可选配置，用于标识数据包结束</div>
          </div>
        </el-form-item>

        <!-- 其他选项 -->
        <el-row>
          <el-col :span="8">
            <el-form-item>
              <el-checkbox v-model="localSettings.scanCodeReading" label="扫码后才读数" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item>
              <el-checkbox v-model="localSettings.portAutoOpen" label="端口自动打开" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <template #footer>
      <el-button size="large" type="primary" @click="handleSave">
        保存
      </el-button>
    </template>
  </vxe-modal>
</template>

<style lang="scss" scoped>
.electronic-scale-modal {
  .el-form-item {
    margin-bottom: 20px;

    .el-radio {
      margin-right: 15px;
    }
  }
}

// 美化提示样式
.setting-label-container {
  display: flex;
  align-items: center;
  gap: 8px;

  .info-badge {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;
    background: linear-gradient(135deg, #409eff, #66b1ff);
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      transform: scale(1.1);
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.4);
    }

    .info-icon {
      color: white;
      font-size: 12px;
    }
  }
}

.custom-tooltip-content {
  .tooltip-title {
    font-weight: 600;
    font-size: 14px;
    color: #fff;
    margin-bottom: 6px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding-bottom: 4px;
  }

  .tooltip-desc {
    font-size: 13px;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.4;
  }
}

.stable-value-container {
  display: flex;
  flex-direction: column;
  gap: 12px;

  .input-group {
    display: flex;
    align-items: center;
    gap: 12px;

    .stable-checkbox {
      flex-shrink: 0;
    }

    .stable-input {
      width: 200px;

      .input-suffix {
        color: #909399;
        font-size: 12px;
      }
    }
  }

  .setting-hint {
    display: flex;
    align-items: flex-start;
    gap: 6px;
    padding: 8px 12px;
    background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
    border: 1px solid #bae6fd;
    border-radius: 6px;

    .hint-icon {
      color: #0ea5e9;
      font-size: 14px;
      margin-top: 1px;
      flex-shrink: 0;
    }

    .hint-text {
      font-size: 12px;
      color: #0369a1;
      line-height: 1.4;
    }
  }
}

.data-setting-container {
  display: flex;
  flex-direction: column;
  gap: 8px;

  .data-input {
    width: 300px;
  }

  .setting-note {
    font-size: 12px;
    color: #909399;
    padding-left: 4px;
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .data-setting-container {
    .data-input {
      width: 100%;
    }
  }
}
</style>
